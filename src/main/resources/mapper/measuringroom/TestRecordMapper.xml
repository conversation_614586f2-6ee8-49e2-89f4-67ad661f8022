<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.psycloud.psyplatform.dao.measuringroom.TestRecordDao">
    <!--添加测评记录-->
    <insert id="addTestRecord" parameterType="cn.psycloud.psyplatform.entity.measuringroom.TestRecordEntity" useGeneratedKeys="true" keyProperty="id">
        insert into psycloud_test_record(
            user_id,
            scale_id,
            state,
            start_time,
            is_valid
        )
        values(
            #{userId,jdbcType=INTEGER},
            #{scaleId,jdbcType=INTEGER},
            0,
            #{startTime,jdbcType=TIMESTAMP},
            1
        )
    </insert>
    <!--更新测评记录-->
    <update id="updateTestRecord" parameterType="map">
        update psycloud_test_record
        set
            state = #{state,jdbcType=TINYINT},
            end_time = #{endTime,jdbcType=TIMESTAMP},
            interpretation = #{interpretation,jdbcType=LONGVARCHAR}
        WHERE id = #{recordId,jdbcType=INTEGER}
    </update>
    <!--更新测评状态-->
    <update id="updateTestState" parameterType="map">
        update psycloud_test_record set state =  #{state,jdbcType=TINYINT} where id = #{recordId,jdbcType=INTEGER}
    </update>
    <!--更新测评开始时间-->
    <update id="updateStartTime" parameterType="map">
        update psycloud_test_record set start_time = #{startTime,jdbcType=TIMESTAMP} where id = #{recordId,jdbcType=INTEGER}
    </update>
    <!--删除-->
    <update id="deleteByRecordId" parameterType="Integer">
        update psycloud_test_record set is_valid = 0 where id =#{recordId,jdbcType=INTEGER}
    </update>
    <!--判断是否异常-->
    <select id="isAbnormal" parameterType="Integer" resultType="Integer">
        select COUNT(*)
        from psycloud_test_score pts
            inner join psycloud_factor pf on pf.id = pts.factor_id
        where pts.record_id = #{recordId}
            and pts.is_abnormal = 1
            and pf.is_lie = 0
    </select>

    <!--更新量表测试次数-->
    <update id="updateTestCount" parameterType="Integer">
        update psycloud_scale set test_count = test_count + 1 where id = #{scaleId}
    </update>
    <!--保存九型人格测试结果-->
    <update id="saveNineHouseRecord" parameterType="map">
        insert into psycloud_ninehouse_record(
            record_id,
            factor_id
        )
        values(
            #{recordId,jdbcType=INTEGER},
            #{factorId,jdbcType=INTEGER}
        )
    </update>
    <select id="getNineHouseList" parameterType="cn.psycloud.psyplatform.dto.measuringroom.NineHouseStatDto" resultType="cn.psycloud.psyplatform.dto.measuringroom.NineHouseStatDto">
        select
            ptr.id as recordId,
            pu.login_name,
            pui.real_name,
            pf.factor_name,
            ptr.start_time as testDate,
            f_GetStructFullName(pui.struct_id) as structFullName
        from psycloud_ninehouse_record pnr
            inner join psycloud_factor pf on pf.id = pnr.factor_id
            inner join psycloud_test_record ptr on ptr.id = pnr.record_id
            inner join psycloud_user_info pui on pui.user_id = ptr.user_id
            inner join psycloud_user pu on pu.user_id = pui.user_id
        where pf.is_valid = 1
            and ptr.is_valid = 1
            and pf.id = #{factorId}
        order by ptr.id desc
    </select>
    <!--保存测评结果解释-->
    <insert id="saveTestRecordExplain" parameterType="cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity">
        insert into psycloud_test_record_explain(
            record_id,
            factor_id,
            interpretation
        )
        values(
            #{recordId,jdbcType=INTEGER},
            #{factorId,jdbcType=INTEGER},
            #{interpretation,jdbcType=LONGVARCHAR}
        )
    </insert>
    <delete id="deleteTestRecordExplain" parameterType="Integer">
        delete from psycloud_test_record_explain where record_id = #{recordId}
    </delete>
    <!--获取测评结果解释-->
    <select id="getTestRecordExplainsByRecordId" parameterType="Integer" resultType="cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity">
        select
            factor_id,
            interpretation
        from psycloud_test_record_explain
        where record_id = #{recordId}
    </select>

    <!--获取因子结果解释列表-->
    <select id="getFactorExplains" parameterType="Integer" resultType="cn.psycloud.psyplatform.entity.measuringroom.TestRecordExplainEntity">
        select
            ptre.record_id as recordId,
            ptre.factor_id as factorId,
            pf.factor_name as factorName,
            pf.factor_type as factorType,
            ptre.interpretation
        from psycloud_test_record_explain ptre
            inner join psycloud_factor pf on pf.id = ptre.factor_id
        where record_id = #{recordId}
        order by factor_id
    </select>
    <!--保存测评报告里的图表-->
    <insert id="saveTestRecordCharts" parameterType="cn.psycloud.psyplatform.entity.measuringroom.TestRecordChartsEntity">
        insert into psycloud_test_record_charts(
            record_id,
            chart_img
        )
        values(
            #{recordId,jdbcType=INTEGER},
            #{chartsImg,jdbcType=VARCHAR}
        )
    </insert>
    <!--清空测评报告图表-->
    <delete id="delTestRecordCharts" parameterType="Integer">
        delete from psycloud_test_record_charts where record_id =#{recordId,jdbcType=INTEGER}
    </delete>
    <select id="getReportCharts" parameterType="Integer" resultType="cn.psycloud.psyplatform.entity.measuringroom.TestRecordChartsEntity">
        select record_id,chart_img as chartsImg from psycloud_test_record_charts where record_id = #{recordId,jdbcType=INTEGER}
    </select>
    <!--根据记录ID查询记录-->
    <resultMap id="recordResultMap" type="cn.psycloud.psyplatform.dto.measuringroom.TestRecordDto">
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="scale_id" property="scaleId" jdbcType="INTEGER" />
        <result column="state" property="state" jdbcType="TINYINT" />
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
        <result column="interpretation" property="interpretation" jdbcType="VARCHAR" />
        <result column="is_valid" property="isValid" jdbcType="TINYINT" />
        <result column="user_id" property="user.userId" jdbcType="INTEGER" />
        <result column="birth" property="user.birth" jdbcType="VARCHAR" />
        <result column="sex" property="user.sex" jdbcType="CHAR" />
        <result column="q_count" property="qCount" jdbcType="INTEGER" />
        <result column="timeInterval" property="timeInterval" jdbcType="INTEGER" />
        <result column="structFullName" property="user.structFullName" jdbcType="VARCHAR" />
        <collection property="scale.listCharts" column="scale_id" select="cn.psycloud.psyplatform.dao.measuringroom.ScaleChartsDao.getListByScaleId" />
    </resultMap>
    <select id="getById" parameterType="Integer" resultMap="recordResultMap">
        select
            ptr.id,
            ptr.user_id,
            ptr.scale_id,
            ptr.state,
            ptr.start_time,
            ptr.end_time,
            f_GetStructFullName(pui.struct_id) as structFullName,
            TimeStampDiff(second,ptr.start_time,ptr.end_time) as timeInterval,
            ptr.interpretation,
            ptr.is_valid,
            pui.birth,
            pui.sex,
            (select count(id) from psycloud_question where is_valid=1 and scale_id=ptr.scale_id) as q_count
        from psycloud_test_record ptr
            inner join psycloud_user_info pui on pui.user_id = ptr.user_id
        where ptr.is_valid =1
            and pui.is_valid =1
            and ptr.id =#{recordId,jdbcType=INTEGER}
    </select>
    <!--根据条件查询测评记录集合-->
    <resultMap id="testRecordResultMap" type="cn.psycloud.psyplatform.dto.measuringroom.TestRecordDto">
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="user_id" property="userId" jdbcType="INTEGER" />
        <result column="login_name" property="loginName" jdbcType="VARCHAR" />
        <result column="real_name" property="realName" jdbcType="VARCHAR" />
        <result column="struct_id" property="structId" jdbcType="INTEGER" />
        <result column="scaleId" property="scaleId" jdbcType="INTEGER" />
        <result column="scale_name" property="scaleName" jdbcType="VARCHAR" />
        <result column="scale_intro" property="scaleIntro" jdbcType="VARCHAR" />
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP" />
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP" />
        <result column="state" property="state" jdbcType="TINYINT" />
        <result column="structFullName" property="structFullName" jdbcType="VARCHAR" />
    </resultMap>
    <select id="getList" parameterType="cn.psycloud.psyplatform.dto.measuringroom.TestRecordDto" resultMap="testRecordResultMap">
        select
            ptr.id,
            pui.user_id,
            pu.login_name,
            pr.role_name,
            pui.real_name,
            pui.struct_id,
            ps.id as scaleId,
            ps.scale_name,
            ps.scale_intro,
            ptr.start_time,
            ptr.end_time,
            ptr.state,
            f_GetStructFullName(pui.struct_id) as structFullName
        from psycloud_test_record ptr
            inner join psycloud_user pu on pu.user_id = ptr.user_id
            inner join psycloud_user_info pui on pui.user_id = ptr.user_id
            inner join psycloud_user_role pur on pur.user_id = pui.user_id
            inner join psycloud_role pr on pr.id = pur.role_id
            inner join psycloud_scale ps on ps.id = ptr.scale_id
            left join psycloud_task_record ptr2 on ptr2.record_id = ptr.id
            left join psycloud_task pt on pt.id = ptr2.task_id
        where ptr.is_valid =1 and pui.is_valid = 1
            <if test="scaleId !=null and scaleId != 0">and ps.id =#{scaleId}</if>
            <if test="state !=null">and ptr.state = #{state}</if>
            <if test="startTime !=null and endTime !=null">and ptr.start_time &gt;= #{startTime} and ptr.start_time &lt;= #{endTime}</if>
            <if test="taskId !=null and taskId !=0">and pt.id = #{taskId}</if>
            <if test="taskId==2">and pui.user_id in (select ptu.user_id from psycloud_task_user ptu where ptu.task_id = #{taskId})</if>
            <if test="roleId==2">and pur.role_id not in(1,3,4)</if>
            <if test="roleId != null and roleId != 0 and roleId!=2">and pur.role_id = #{roleId}</if>
            <if test="loginName !=null and loginName !=''">and pu.login_name like CONCAT('%',#{loginName},'%')</if>
            <if test="realName !=null and realName !=''">and pui.real_name like CONCAT('%',#{realName},'%')</if>
            <if test="sex != null and sex !='' ">and pui.sex =#{sex} </if>
            <if test="userId!=null and userId!=0">and pu.user_id = #{userId}</if>
            <if test="childStructs!=null and childStructs.size()>0">
                and pui.struct_id in
                <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        order by ptr.id desc
    </select>
    <select id="getMyRecords" parameterType="cn.psycloud.psyplatform.dto.measuringroom.TestRecordDto" resultType="cn.psycloud.psyplatform.dto.measuringroom.TestRecordDto">
        select
            ptr.id,
            ps.scale_name,
            ptr.start_time,
            ptr.end_time,
            ptr.state
        from psycloud_test_record ptr
            inner join psycloud_user_info pui on pui.user_id = ptr.user_id
            inner join psycloud_scale ps on ps.id = ptr.scale_id
        where ptr.is_valid =1
          and pui.is_valid = 1
          and pui.user_id = #{userId}
          and ptr.id not in (
            select ptr2.record_id
            from psycloud_task_record ptr2
                inner join psycloud_task pt on pt.id = ptr2.task_id
                inner join psycloud_test_record ptr3 on ptr3.id = ptr2.record_id
            where pt.is_valid=0 and ptr3.user_id =#{userId}
        )
        <if test="scaleName !=null and scaleName != ''">and ps.scale_name =#{scaleName}</if>
        <if test="state !=null">and ptr.state = #{state}</if>
        <if test="startTime !=null and endTime !=null">and ptr.start_time &gt;= #{startTime} and ptr.start_time &lt;= #{endTime}</if>
        order by ptr.id desc
    </select>
    <!--获取发送异常消息的信息-->
    <resultMap id="abnormalNotifyInfoMap" type="java.util.Map">
        <result column="real_name" property="realName" jdbcType="VARCHAR" />
        <result column="scale_name" property="scaleName" jdbcType="VARCHAR" />
        <result column="person_in_charge" property="userId" jdbcType="INTEGER" />
    </resultMap>
    <select id="getAbnormalNotifyInfo" parameterType="Integer" resultMap="abnormalNotifyInfoMap">
        select
            pui.real_name,
            ps.scale_name ,
            pt.person_in_charge
        from psycloud_test_record ptr
                 inner join psycloud_user_info pui on pui.user_id = ptr.user_id
                 inner join psycloud_scale ps on ps.id = ptr.scale_id
                 inner join psycloud_task_record ptr2 on ptr2.record_id = ptr.id
                 inner join psycloud_task pt on pt.id = ptr2.task_id
        where ptr.id = #{recordId}
    </select>

    <!--导出测评记录-->
    <select id="getExportTestRecordList" parameterType="cn.psycloud.psyplatform.dto.measuringroom.TestRecordDto" resultType="cn.psycloud.psyplatform.dto.measuringroom.ExportTestRecordDto">
        select
            pu.login_name,
            pui.real_name,
            pui.sex,
            f_GetStructFullName(pui.struct_id) as structFullName,
            ps.scale_name,
            ptr.start_time,
            ptr.end_time,
            case ptr.state when 0 then '未完成' when 1 then '完成' when 2 then '测谎未通过' end as state
        from psycloud_test_record ptr
                 inner join psycloud_user pu on pu.user_id = ptr.user_id
                 inner join psycloud_user_info pui on pui.user_id = ptr.user_id
                 inner join psycloud_scale ps on ps.id = ptr.scale_id
                 left join psycloud_task_record ptr2 on ptr2.record_id = ptr.id
                 left join psycloud_task pt on pt.id = ptr2.task_id
        where ptr.is_valid =1 and pui.is_valid = 1
        <if test="scaleId !=null and scaleId != 0">and ps.id =#{scaleId}</if>
        <if test="state !=null">and ptr.state = #{state}</if>
        <if test="startTime !=null and endTime !=null">and ptr.start_time &gt;= #{startTime} and ptr.start_time &lt;= #{endTime}</if>
        <if test="taskId !=null and taskId !=0">and pt.id = #{taskId}</if>
        <if test="roleId==2">and pur.role_id not in(1,3,4)</if>
        <if test="roleId != null and roleId != 0 and roleId!=2">and pur.role_id = #{roleId}</if>
        <if test="loginName !=null and loginName !=''">and pu.login_name like CONCAT('%',#{loginName},'%')</if>
        <if test="realName !=null and realName !=''">and pui.real_name like CONCAT('%',#{realName},'%')</if>
        <if test="sex != null and sex !='' ">and pui.sex =#{sex} </if>
        <if test="userId!=null and userId!=0">and pu.user_id = #{userId}</if>
        <if test="childStructs!=null and childStructs.size()>0">
            and pui.struct_id in
            <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by ptr.id desc
    </select>
    <!--批量导出测试报告获取测评记录id集合-->
    <resultMap id="recordIdsForBatchExportMap" type="java.util.Map">
        <result column="id" property="id" jdbcType="INTEGER" />
    </resultMap>
    <select id="getRecordIdsForBatchExport" parameterType="cn.psycloud.psyplatform.dto.measuringroom.TestRecordDto" resultMap="recordIdsForBatchExportMap">
        select ptr.id
        from psycloud_test_record ptr
            inner join psycloud_user pu on pu.user_id = ptr.user_id
            inner join psycloud_user_info pui on pui.user_id = ptr.user_id
            inner join psycloud_scale ps on ps.id = ptr.scale_id
            left join psycloud_task_record ptr2 on ptr2.record_id = ptr.id
            left join psycloud_task pt on pt.id = ptr2.task_id
            where ptr.is_valid =1
              and pui.is_valid = 1
              and ptr.state in (1,2)
                <if test="scaleId !=null and scaleId != 0">and ps.id =#{scaleId}</if>
                <if test="startTime !=null and endTime !=null">and ptr.start_time &gt;= #{startTime} and ptr.start_time &lt;= #{endTime}</if>
                <if test="taskId !=null and taskId !=0">and pt.id = #{taskId}</if>
                <if test="roleId==2">and pur.role_id not in(1,3,4)</if>
                <if test="roleId != null and roleId != 0 and roleId!=2">and pur.role_id = #{roleId}</if>
                <if test="loginName !=null and loginName !=''">and pu.login_name like CONCAT('%',#{loginName},'%')</if>
                <if test="realName !=null and realName !=''">and pui.real_name like CONCAT('%',#{realName},'%')</if>
                <if test="sex != null and sex !='' ">and pui.sex =#{sex} </if>
                <if test="userId!=null and userId!=0">and pu.user_id = #{userId}</if>
                <if test="childStructs!=null and childStructs.size()>0">
                    and pui.struct_id in
                    <foreach collection="childStructs" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
    </select>

    <!--查询用户的测评记录（生成档案）-->
    <select id="getListForArchive" parameterType="Integer" resultType="cn.psycloud.psyplatform.dto.archiveroom.MeasuringRecordDto">
        select
            ptr.start_time as startTime,
            ptr.end_time as endTime,
            TimeStampDiff(second,ptr.start_time,ptr.end_time) as timeInterval,
            ps.scale_name,
            ptr.interpretation
        from psycloud_test_record ptr
             inner join psycloud_task_record ptr2 on ptr2.record_id = ptr.id
             inner join psycloud_scale ps on ps.id = ptr.scale_id
        where ptr.state = 1
            and ptr.is_valid = 1
            and ptr.user_id= #{userId}
        order by ptr.id asc
    </select>

    <!-- 根据任务id和用户id查询测评记录id-->
    <select id="getRecordIdByTaskIdAndUserId" parameterType="java.util.Map" resultType="java.lang.Integer">
        select ptr.id
        from psycloud_test_record ptr
            inner join psycloud_task_record ptr2 on ptr2.record_id = ptr.id
        where ptr2.task_id= #{taskId}
            and ptr.scale_id = #{scaleId}
            and ptr.user_id =  #{userId}
            and ptr.is_valid = 1
    </select>

    <!--更新测评时间-->
    <update id="updateTestTime" parameterType="java.util.Map">
        update
            psycloud_test_record
        set start_time = #{startTime,jdbcType=TIMESTAMP},
            end_time = #{endTime,jdbcType=TIMESTAMP}
        where id = #{recordId,jdbcType=INTEGER}
    </update>
</mapper>